// Package gui 提供图形用户界面相关的功能，基于Fyne框架实现。
// 包含主窗口、视频渲染、用户交互等GUI组件的实现。
package gui

import (
	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/dialog"
	"github.com/golang/glog"
)

// showInfoDialog 显示信息对话框
// info: 要显示的信息内容
// parent: 父窗口
func showInfoDialog(info string, parent fyne.Window) {
	dialog.ShowInformation("提示", info, parent)
}

// showConfirmDialog 显示确认对话框并返回用户选择
// info: 要显示的确认信息
// parent: 父窗口
// 返回值: 用户是否确认（true表示确认，false表示取消）
func showConfirmDialog(info string, parent fyne.Window) bool {
	resultchan := make(chan bool)
	go dialog.ShowConfirm("确认", info, func(b bool) {
		resultchan <- b
	}, parent)

	flag := <-resultchan
	glog.Infof("common gui: got confirm flag:%v", flag)
	return flag
}
