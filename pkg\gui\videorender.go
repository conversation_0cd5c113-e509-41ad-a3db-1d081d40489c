package gui

import (
	"fmt"
	"image"
	"image/color"
	"image/draw"
	"image/png"
	"os"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/canvas"
	"fyne.io/fyne/v2/container"
)

func (v *VideoWin) playFullscreenAnimation(a fyne.App) {
	// 新建一个只用于播放的窗口（不设置菜单、按钮等）
	win := a.NewWindow("")

	var width, height float32 = 800.00, 600.00

	// 背景层：静态图像（一次设置）
	bg := image.NewNRGBA(image.Rect(0, 0, int(width), int(height)))
	// 填充为绿色背景示例
	draw.Draw(bg, bg.Bounds(), &image.Uniform{color.RGBA{R: 135, G: 206, B: 235, A: 255}}, image.Point{}, draw.Src)
	bgCanvas := canvas.NewImageFromImage(bg)
	bgCanvas.FillMode = canvas.ImageFillStretch

	spriteBuf := image.NewNRGBA(image.Rect(0, 0, int(width), int(height)))

	pngimages := []image.Image{}
	for _, path := range imagePaths {
		// 打开文件
		file, err := os.Open(path)
		if err != nil {
			fmt.Printf("无法打开文件:%v\n", err)
			return
		}
		defer file.Close()

		// 解码PNG
		img, err := png.Decode(file)
		if err != nil {
			fmt.Printf("无法解码PNG:%v\n", err)
			return
		}
		pngimages = append(pngimages, img)
	}

	// 使用 canvas.Raster 绘制每一帧（这里只是示例：不断变色）
	index := 0
	raster := canvas.NewRaster(func(w, h int) image.Image {
		if index < len(pngimages) {
			img := pngimages[index]
			index++
			return img
		} else {
			index = 0
			img := pngimages[index]
			return img
		}
	})

	// 全屏显示，仅包含动画画布（Max 用来拉伸到整窗）
	// win.SetContent(container.NewMax(raster))
	// 布局：用 Max 层叠背景与精灵层，再加 UI 层
	content := container.NewStack(bgCanvas, raster)
	win.SetContent(content)
	// win.Resize(fyne.NewSize(width, height))
	// win.Show()

	// 设置为全屏（会隐藏标题栏/装饰）
	win.SetFullScreen(true)

	// 允许按 Esc 退出全屏并关闭窗口
	c := win.Canvas()
	c.SetOnTypedKey(func(ev *fyne.KeyEvent) {
		if ev.Name == fyne.KeyEscape {
			win.Close()
		}
	})

	// 动画更新协程，tick 刷新 raster
	done := make(chan struct{})
	go func() {
		ticker := time.NewTicker(time.Millisecond * 40) // ~25 FPS
		defer ticker.Stop()
		for {
			select {
			case <-ticker.C:
				fyne.Do(func() {
					raster.Refresh()
				})
			case <-done:
				return
			}
		}
	}()

	// 当窗口关闭时停止动画协程
	win.SetOnClosed(func() {
		close(done)
	})

	win.Show()
}
