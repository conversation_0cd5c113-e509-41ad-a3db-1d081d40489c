package gui

import (
	"fmt"
	"image/color"
	"os"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/canvas"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/layout"
	"fyne.io/fyne/v2/widget"
	"github.com/hajimehoshi/go-mp3"
	"github.com/hajimehoshi/oto/v2"
	"github.com/youpy/go-wav"

	"github.com/tming/scenevideo/pkg/data"
)

var (
	// imagePaths 角色图片路径列表
	// imagePaths = []string{
	// 	"resources/scene1/male_lead_speak1.png",
	// 	"resources/scene1/male_lead_speak2.png",
	// 	"resources/scene1/male_lead_speak3.png",
	// }
	// imagePaths = []string{
	// 	"resources/scene1/cat_close.png",
	// 	"resources/scene1/cat_half_open.png",
	// 	"resources/scene1/cat_full_open.png",
	// }
	imagePaths = []string{
		// "resources/scene1/cat13.png",
		// "resources/scene1/cat13.png",
		// "resources/scene1/cat13.png",

		// "D:\\tomtian\\video_engine\\ali_img_test\\p2v2\\data\\catgirl\\speak_output\\frame_0001.jpg",
		// // "D:\\tomtian\\video_engine\\ali_img_test\\p2v2\\data\\catgirl\\speak_output\\frame_0002.jpg",
		// // "D:\\tomtian\\video_engine\\ali_img_test\\p2v2\\data\\catgirl\\speak_output\\frame_0003.jpg",
		// // "D:\\tomtian\\video_engine\\ali_img_test\\p2v2\\data\\catgirl\\speak_output\\frame_0004.jpg",
		// "D:\\tomtian\\video_engine\\ali_img_test\\p2v2\\data\\catgirl\\speak_output\\frame_0005.jpg",
		// "D:\\tomtian\\video_engine\\ali_img_test\\p2v2\\data\\catgirl\\speak_output\\frame_0006.jpg",
		// "D:\\tomtian\\video_engine\\ali_img_test\\p2v2\\data\\catgirl\\speak_output\\frame_0007.jpg",
		// "D:\\tomtian\\video_engine\\ali_img_test\\p2v2\\data\\catgirl\\speak_output\\frame_0008.jpg",
		// // "D:\\tomtian\\video_engine\\ali_img_test\\p2v2\\data\\catgirl\\speak_output\\frame_0009.jpg",
		// // "D:\\tomtian\\video_engine\\ali_img_test\\p2v2\\data\\catgirl\\speak_output\\frame_0010.jpg",
		// // "D:\\tomtian\\video_engine\\ali_img_test\\p2v2\\data\\catgirl\\speak_output\\frame_0011.jpg",
		// // "D:\\tomtian\\video_engine\\ali_img_test\\p2v2\\data\\catgirl\\speak_output\\frame_0012.jpg",
		// // "D:\\tomtian\\video_engine\\ali_img_test\\p2v2\\data\\catgirl\\speak_output\\frame_0013.jpg",
		// // "D:\\tomtian\\video_engine\\ali_img_test\\p2v2\\data\\catgirl\\speak_output\\frame_0014.jpg",
		// // "D:\\tomtian\\video_engine\\ali_img_test\\p2v2\\data\\catgirl\\speak_output\\frame_0015.jpg",
		// // "D:\\tomtian\\video_engine\\ali_img_test\\p2v2\\data\\catgirl\\speak_output\\frame_0016.jpg",
		// // "D:\\tomtian\\video_engine\\ali_img_test\\p2v2\\data\\catgirl\\speak_output\\frame_0017.jpg",
		// // "D:\\tomtian\\video_engine\\ali_img_test\\p2v2\\data\\catgirl\\speak_output\\frame_0018.jpg",
		// // "D:\\tomtian\\video_engine\\ali_img_test\\p2v2\\data\\catgirl\\speak_output\\frame_0019.jpg",
		// // "D:\\tomtian\\video_engine\\ali_img_test\\p2v2\\data\\catgirl\\speak_output\\frame_0020.jpg",
		// // "D:\\tomtian\\video_engine\\ali_img_test\\p2v2\\data\\catgirl\\speak_output\\frame_0021.jpg",
		// // "D:\\tomtian\\video_engine\\ali_img_test\\p2v2\\data\\catgirl\\speak_output\\frame_0022.jpg",
		// // "D:\\tomtian\\video_engine\\ali_img_test\\p2v2\\data\\catgirl\\speak_output\\frame_0023.jpg",
		// // "D:\\tomtian\\video_engine\\ali_img_test\\p2v2\\data\\catgirl\\speak_output\\frame_0024.jpg",
		// // "D:\\tomtian\\video_engine\\ali_img_test\\p2v2\\data\\catgirl\\speak_output\\frame_0025.jpg",

		"D:\\tomtian\\video_engine\\ali_img_test\\p2v2\\data\\catgirl\\speak_output\\frame_0001.png",
		"D:\\tomtian\\video_engine\\ali_img_test\\p2v2\\data\\catgirl\\speak_output\\frame_0006.png",
		"D:\\tomtian\\video_engine\\ali_img_test\\p2v2\\data\\catgirl\\speak_output\\frame_0007.png",
		"D:\\tomtian\\video_engine\\ali_img_test\\p2v2\\data\\catgirl\\speak_output\\frame_0008.png",
	}

	// speechtext1 默认语音文本内容
	// speechtext1 = "虽然前方拥堵，您仍然在最优道路上"
	// testwavefile = "resources/scene1/speech1.mp3"

	speechtext1  = "长风破浪会有时，直挂云帆济沧海。"
	testwavefile = "resources/scene1/zhibei_emo_happy.wav"

	// localFrameCounter 本地帧计数器
	localFrameCounter int32
)

// VideoWin 表示视频渲染窗口组件
type VideoWin struct {
	parent fyne.Window

	mainContainer fyne.CanvasObject // 主容器

	controls       *fyne.Container // 控制按钮容器
	startBtn       *widget.Button  // 开始按钮
	stopBtn        *widget.Button  // 停止按钮
	fullBtn        *widget.Button  // 全屏测试按钮
	yamlLabel      *widget.Label   // YAML文件路径显示
	debugInfoLabel *widget.Label   // 位置显示

	speechContainer *fyne.Container // 语音文本容器
	speechLabel     *widget.Label   // 语音文本显示

	content *fyne.Container   // 动画容器
	border  *canvas.Rectangle // 动画边框

	// 动画相关变量，需要进一步优化
	x, y             float32       // 初始位置
	running          bool          // 动画运行状态
	mu               sync.Mutex    // 用于安全访问共享变量
	stopCh           chan struct{} // 停止信号通道
	imageIndex       int           // 当前显示的图片索引
	frameCounter     int           // 帧计数器，用于控制图片切换频率
	images           []*canvas.Image
	defaultImageSize int

	// 音频相关变量
	audioContext *oto.Context
	audioPlayer  oto.Player
	audioMu      sync.Mutex // 音频操作的互斥锁
}

func NewVideoWin(parent fyne.Window) *VideoWin {
	win := VideoWin{
		parent:           parent,
		stopCh:           make(chan struct{}),
		defaultImageSize: 600,
	}

	win.initCanvas()

	return &win
}

// NewCanvas 创建新的画布组件，用于视频渲染和动画显示
// 返回值: 配置好的画布对象
func (v *VideoWin) initCanvas() {
	// 初始化各个部分
	v.initVideo()
	v.initControl()
	v.initSpeech()

	// 主布局（控制按钮在上，动画区域在中间，语音文本在下）
	v.mainContainer = container.NewBorder(v.controls, v.speechContainer, nil, nil, v.content)

	// 窗口大小变化处理
	v.parent.Canvas().SetOnTypedKey(func(k *fyne.KeyEvent) {
		if k.Name == fyne.KeyF11 {
			v.parent.SetFullScreen(!v.parent.FullScreen())
			// 更新边框大小
			v.updateBorderSize()
		}
	})

	// 窗口关闭时清理资源
	v.parent.SetOnClosed(func() {
		close(v.stopCh) // 关闭通道，通知动画goroutine退出
		v.stopAudio()   // 停止音频播放
		// 注意：oto.Context 不需要手动关闭，会自动清理
	})

	// 初始更新边框大小
	v.updateBorderSize()

	// 启动动画goroutine
	go v.animate()

	// 启动调试信息刷新goroutine
	go v.freshdebugtext()

	// 添加窗口大小变化监听器
	go v.checkWindowSize()
}

func (v *VideoWin) getCanvas() fyne.CanvasObject {
	return v.mainContainer
}

func (v *VideoWin) initVideo() {
	// 定义显示对象的尺寸
	objectSize := float32(v.defaultImageSize)

	for _, path := range imagePaths {
		img := canvas.NewImageFromFile(path)
		if img != nil {
			img.FillMode = canvas.ImageFillContain
			img.Resize(fyne.NewSize(objectSize, objectSize))
			img.Move(fyne.NewPos(50, 50))
			v.images = append(v.images, img)
		} else {
			fmt.Printf("加载图片失败: %s\n", path)
		}
	}

	// 创建动画区域边框
	v.border = canvas.NewRectangle(color.Transparent)
	v.border.StrokeColor = color.NRGBA{R: 0, G: 0, B: 0, A: 255} // 黑色边框
	v.border.StrokeWidth = 2                                     // 边框宽度
	// v.border.FillColor = color.Transparent                       // 透明填充
	v.border.FillColor = color.NRGBA{R: 135, G: 206, B: 235, A: 255} // 天空蓝，避免图片的背景不是透明的

	// 创建容器并添加边框和当前显示对象
	v.content = container.NewWithoutLayout(v.border)
}

func (v *VideoWin) initControl() {
	// 创建控制按钮
	v.startBtn = widget.NewButton("开始渲染", nil)
	v.stopBtn = widget.NewButton("停止渲染", nil)
	v.fullBtn = widget.NewButton("全屏测试", func() {
		v.playFullscreenAnimation(gApp)
	})
	v.yamlLabel = widget.NewLabel(fmt.Sprintf("当前yaml: [%s]", gSelectedYamlPath))

	// 添加状态标签显示位置
	v.debugInfoLabel = widget.NewLabel(fmt.Sprintf("位置: (%.1f, %.1f)", 50.0, 50.0))

	// 按钮容器
	v.controls = container.NewVBox(
		container.NewHBox(
			v.startBtn,
			v.stopBtn,
			v.fullBtn,
			v.yamlLabel,
			layout.NewSpacer(),
		),
		v.debugInfoLabel, // 位置显示
	)

	// 按钮事件处理
	v.startBtn.OnTapped = v.onStartBtnTapped
	v.stopBtn.OnTapped = v.onStopBtnTapped

	// 初始按钮状态
	v.stopBtn.Disable()
}

func (v *VideoWin) updateYaml() {
	v.yamlLabel.SetText(fmt.Sprintf("当前yaml: [%s]", gSelectedYamlPath))
}

func (v *VideoWin) initSpeech() {
	// 创建语音文本显示区域
	v.speechLabel = widget.NewLabel(speechtext1)
	v.speechLabel.Wrapping = fyne.TextWrapWord     // 启用文本换行
	v.speechLabel.Alignment = fyne.TextAlignCenter // 居中对齐

	// 创建语音文本容器，添加一些样式和间距
	v.speechContainer = container.NewVBox(
		widget.NewSeparator(), // 添加分隔线
		v.speechLabel,
		widget.NewLabel(""), // 添加底部间距
	)
}

// onStartBtnTapped 开始按钮事件处理
func (v *VideoWin) onStartBtnTapped() {
	// TODO: 检查是否已选择YAML文件，然后根据yaml内容依次进行渲染
	if gSelectedYamlPath == "" {
		showInfoDialog("请通过菜单选择需要渲染的yaml文件", v.parent)
		return
	}

	scene, err := data.ResolveSceneYaml(gSelectedYamlPath)
	if err != nil {
		showInfoDialog(fmt.Sprintf("解析yaml文件失败: %v", err), v.parent)
		return
	} else {
		fmt.Printf("解析yaml文件成功: %v\n", *scene)
	}

	v.mu.Lock()
	v.running = true
	v.mu.Unlock()
	v.startBtn.Disable()
	v.stopBtn.Enable()

	// 更新语音文本显示状态
	v.speechLabel.SetText("🔊 " + speechtext1) // 添加音频图标

	// 播放音频
	go func() {
		if err := v.playAudio(testwavefile); err != nil {
			fmt.Printf("播放音频失败: %v\n", err)
		}
	}()
}

func (v *VideoWin) onStopBtnTapped() {
	v.mu.Lock()
	v.running = false
	v.mu.Unlock()
	v.stopBtn.Disable()
	v.startBtn.Enable()

	// 停止音频播放
	v.stopAudio()

	// 更新语音文本显示状态
	v.speechLabel.SetText("⏹️ " + speechtext1) // 添加停止图标
}

// playAudio 播放MP3音频文件
func (v *VideoWin) playAudio(filename string) error {
	v.audioMu.Lock()
	defer v.audioMu.Unlock()

	// 停止当前播放的音频
	if v.audioPlayer != nil {
		v.audioPlayer.Close()
		v.audioPlayer = nil
	}

	// 打开MP3文件
	file, err := os.Open(filename)
	if err != nil {
		fmt.Printf("打开音频文件失败: %v\n", err)
		return err
	}

	if strings.HasSuffix(filename, ".mp3") {
		// 解码MP3
		decoder, err := mp3.NewDecoder(file)
		if err != nil {
			file.Close()
			fmt.Printf("解码MP3文件失败: %v\n", err)
			return err
		}

		// 获取MP3文件的音频参数
		sampleRate := decoder.SampleRate()
		fmt.Printf("MP3文件参数 - 采样率: %d Hz, 长度: %d 样本\n", sampleRate, decoder.Length())

		// 总是重新创建音频上下文以确保参数正确
		if v.audioContext != nil {
			// 如果已有上下文，先清理
			v.audioContext = nil
		}

		// 根据MP3文件的实际参数创建音频上下文
		// 参数：采样率，声道数(2=立体声)，样本大小(2字节=16位)
		audioContext, ready, err := oto.NewContext(sampleRate, 2, 2)
		if err != nil {
			file.Close()
			fmt.Printf("创建音频上下文失败: %v\n", err)
			return err
		}

		// 等待音频上下文准备就绪
		<-ready
		fmt.Printf("音频上下文创建成功，采样率: %d Hz\n", sampleRate)

		// 创建音频播放器
		v.audioPlayer = audioContext.NewPlayer(decoder)
	} else if strings.HasSuffix(filename, ".wav") {
		// 解码MP3
		reader := wav.NewReader(file)
		if err != nil {
			file.Close()
			fmt.Printf("解码MP3文件失败: %v\n", err)
			return err
		}

		// 获取MP3文件的音频参数
		// 读取WAV文件格式信息
		format, err := reader.Format()
		if err != nil {
			file.Close()
			fmt.Printf("解码MP3文件失败: %v\n", err)
			return err
		}
		sampleRate := format.SampleRate
		fmt.Printf("MP3文件参数 - 采样率: %d Hz\n", sampleRate)

		// 总是重新创建音频上下文以确保参数正确
		if v.audioContext != nil {
			// 如果已有上下文，先清理
			v.audioContext = nil
		}

		// 根据MP3文件的实际参数创建音频上下文
		// 参数：采样率，声道数(2=立体声)，样本大小(2字节=16位)
		// audioContext, ready, err := oto.NewContext(int(sampleRate), 2, 2)
		audioContext, ready, err := oto.NewContext(int(format.SampleRate), int(format.NumChannels), 2)
		if err != nil {
			file.Close()
			fmt.Printf("创建音频上下文失败: %v\n", err)
			return err
		}

		// 等待音频上下文准备就绪
		<-ready
		fmt.Printf("音频上下文创建成功，采样率: %d Hz\n", sampleRate)

		// 创建音频播放器
		v.audioPlayer = audioContext.NewPlayer(reader)
	}

	// 在新的goroutine中播放音频
	go func() {
		defer func() {
			v.audioMu.Lock()
			if v.audioPlayer != nil {
				v.audioPlayer.Close()
				v.audioPlayer = nil
			}
			v.audioMu.Unlock()
			file.Close()
		}()

		v.audioPlayer.Play()

		// 等待播放完成
		for v.audioPlayer.IsPlaying() {
			time.Sleep(time.Millisecond * 100)
		}

		fmt.Printf("音频播放完成\n")
	}()

	return nil
}

// stopAudio 停止音频播放
func (v *VideoWin) stopAudio() {
	v.audioMu.Lock()
	defer v.audioMu.Unlock()
	if v.audioPlayer != nil {
		v.audioPlayer.Close()
		v.audioPlayer = nil
	}
}

// updateBorderSize 更新边框大小以适应窗口变化
func (v *VideoWin) updateBorderSize() {
	// 获取窗口尺寸
	windowWidth := float32(v.parent.Canvas().Size().Width - 8)
	windowHeight := float32(v.parent.Canvas().Size().Height - 154)

	// 设置边框位置和大小
	fyne.Do(func() {
		v.border.Resize(fyne.NewSize(windowWidth, windowHeight))
	})
	v.border.Move(fyne.NewPos(0, 0))
}

// animate 动画循环函数，处理图片切换和位置更新
func (v *VideoWin) animate() {
	// 预分配变量避免GC压力和减少系统调用
	var pos fyne.Position
	var lastImageSwitchFrame int32

	// 如果没有成功加载图片，创建一个默认的小球
	var currentDisplayObject fyne.CanvasObject
	if len(v.images) > 0 {
		currentDisplayObject = v.images[0]
		v.content.Add(currentDisplayObject)
	}

	// 使用更高精度的定时器，减少系统调用开销
	ticker := time.NewTicker(time.Millisecond * 16)
	defer ticker.Stop()

	for {
		select {
		case <-v.stopCh:
			return // 收到停止信号，退出goroutine
		case <-ticker.C:
			// 原子递增帧计数器，避免锁竞争
			atomic.AddInt32(&localFrameCounter, 1)
			currentFrame := atomic.LoadInt32(&localFrameCounter)

			// 最小化锁的持有时间
			v.mu.Lock()
			isRunning := v.running
			currentX, currentY := v.x, v.y
			v.mu.Unlock()

			if !isRunning {
				continue
			}

			// 图片切换逻辑：每30帧切换一次图片
			if len(v.images) > 0 && currentFrame-lastImageSwitchFrame >= 30 {
				v.mu.Lock()
				v.imageIndex = (v.imageIndex + 1) % len(v.images)

				// 只在需要时切换显示对象
				if currentDisplayObject != v.images[v.imageIndex] {
					v.content.Remove(currentDisplayObject)
					currentDisplayObject = v.images[v.imageIndex]
					pos.X, pos.Y = currentX, currentY
					currentDisplayObject.Move(pos)
					v.content.Add(currentDisplayObject)
					fyne.Do(func() {
						currentDisplayObject.Refresh()
					})
					lastImageSwitchFrame = currentFrame
				}
				v.mu.Unlock()
			}
		}
	}
}

// 创建一个定时器来检查窗口大小变化（降低检查频率）
func (v *VideoWin) checkWindowSize() {
	var lastWindowSize fyne.Size
	lastWindowSize = v.parent.Canvas().Size()

	ticker := time.NewTicker(time.Millisecond * 100) // 每100ms检查一次
	defer ticker.Stop()

	for {
		select {
		case <-v.stopCh:
			return
		case <-ticker.C:
			currentSize := v.parent.Canvas().Size()
			if currentSize.Width != lastWindowSize.Width || currentSize.Height != lastWindowSize.Height {
				v.updateBorderSize()
				lastWindowSize = currentSize
			}
		}
	}
}

// freshdebugtext 更新调试信息显示
func (v *VideoWin) freshdebugtext() {
	startTime := time.Now()

	// 使用更高精度的定时器，减少系统调用开销
	ticker := time.NewTicker(time.Millisecond * 1000)
	defer ticker.Stop()

	for {
		select {
		case <-v.stopCh:
			return // 收到停止信号，退出goroutine
		case <-ticker.C:
			if !v.running {
				continue
			}

			v.mu.Lock()
			currentFrame := atomic.LoadInt32(&localFrameCounter)
			v.frameCounter = int(currentFrame)
			// 更新调试信息显示
			seconds := time.Since(startTime).Seconds()
			fyne.Do(func() {
				v.debugInfoLabel.SetText(fmt.Sprintf("帧:%d,seconds:%.1f,帧率:%.1f", v.frameCounter, seconds, float64(v.frameCounter)/seconds))
			})
			v.mu.Unlock()
		}
	}
}
